<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>车间数字化管理系统</title>
    <link rel="stylesheet" href="style.css?v=20250723">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
</head>
<body>
    <!-- 背景Three.js容器 -->
    <div id="three-bg"></div>

    <!-- 主要内容容器 -->
    <div class="dashboard-container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
            </div>
            <h1 class="main-title">车间数字化管理系统</h1>
            <div class="header-right">
                <div class="current-time" id="current-time">
                    <span class="date" id="current-date">--</span>
                    <span class="time" id="current-time-display">--:--:--</span>
                </div>
                <div class="admin-link-container">
                    <a href="/admin" class="admin-link" title="后台管理">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                            <circle cx="12" cy="12" r="3"></circle>
                        </svg>
                        <span>后台管理</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 统计数据卡片 - 保持在顶部 -->
            <div class="stats-cards">
                <div class="stat-card running clickable" onclick="showDevicesByStatus('running')">
                    <h4>运行数量</h4>
                    <span class="stat-number" id="running-count">-</span>
                </div>
                <div class="stat-card idle clickable" onclick="showDevicesByStatus('idle')">
                    <h4>空闲数量</h4>
                    <span class="stat-number" id="idle-count">-</span>
                </div>
                <div class="stat-card alarm clickable" onclick="showDevicesByStatus('alarm')">
                    <h4>报警数量</h4>
                    <span class="stat-number" id="alarm-count">-</span>
                </div>
                <div class="stat-card offline clickable" onclick="showDevicesByStatus('offline')">
                    <h4>离线数量</h4>
                    <span class="stat-number" id="offline-count">-</span>
                </div>
            </div>

            <!-- 左右两个设备表格区域 -->
            <div class="dual-panel-container">
                <!-- 左侧两个表格的统一容器 -->
                <div class="device-tables-container">
                    <!-- 表格控制区域 - 移动到表格容器内部左上角 
                    <div class="table-controls-overlay">
                        <div class="controls-left">
                            <div class="line-selector-container">
                                <label for="production-line-select-left" class="line-selector-label">生产线:</label>
                                <select id="production-line-select-left" class="line-select-small">
                                    <option value="all">全部生产线</option>
                                </select>
                            </div>
                            <button id="refresh-data-btn-left" class="refresh-btn-small" title="刷新数据">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="23 4 23 10 17 10"></polyline>
                                    <polyline points="1 20 1 14 7 14"></polyline>
                                    <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"></path>
                                </svg>
                            </button>
                        </div>
                    </div>-->
                    <!-- 左侧设备表格 -->
                    <section class="left-device-panel">
                        <div class="device-list-section">
                            <div class="device-table-container" id="device-table-container-left">
                                <table class="device-table" id="device-table-left">
                                    <thead>
                                        <tr>
                                            <th>设备名称</th>
                                            <th>加工产品及工序</th>
                                            <th>今日产出</th>
                                            <th>当前OEE</th>
                                            <th>设备OEE</th>
                                            <th>开机率</th>
                                            <th>设备状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="device-table-body-left">
                                        <tr>
                                            <td colspan="7" class="loading-cell">正在加载设备数据...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </section>

                    <!-- 右侧设备表格 -->
                    <section class="right-device-panel">
                        <div class="device-list-section">
                            <div class="device-table-container" id="device-table-container-right">
                                <table class="device-table" id="device-table-right">
                                    <thead>
                                        <tr>
                                            <th>设备名称</th>
                                            <th>加工产品及工序</th>
                                            <th>今日产出</th>
                                            <th>当前OEE</th>
                                            <th>设备OEE</th>
                                            <th>开机率</th>
                                            <th>设备状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="device-table-body-right">
                                        <tr>
                                            <td colspan="7" class="loading-cell">正在加载设备数据...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </section>
                </div>

                <!-- 右侧信息面板 -->
                <section class="right-panel">
                    <!-- 日历移到最上面 -->
                    <div class="panel-card calendar-panel-card">
                        <h3 class="panel-title">📅 安全生产日历</h3>
                        <div class="safety-calendar-container">
                            <div class="calendar-header">
                                <button id="prev-month" class="calendar-nav-btn">‹</button>
                                <span id="current-month-year" class="calendar-title">2024年1月</span>
                                <button id="next-month" class="calendar-nav-btn">›</button>
                            </div>
                            <div class="calendar-grid">
                                <div class="calendar-weekdays">
                                    <div class="weekday">日</div>
                                    <div class="weekday">一</div>
                                    <div class="weekday">二</div>
                                    <div class="weekday">三</div>
                                    <div class="weekday">四</div>
                                    <div class="weekday">五</div>
                                    <div class="weekday">六</div>
                                </div>
                                <div class="calendar-days" id="calendar-days">
                                    <!-- 日期将通过JavaScript动态生成 -->
                                </div>
                            </div>
                            <div class="calendar-legend">
                                <div class="legend-item">
                                    <div class="legend-color normal"></div>
                                    <span>正常</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color alarm"></div>
                                    <span>有报警</span>
                                </div>
                                <div class="legend-item">
                                    <div class="legend-color today"></div>
                                    <span>今天</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 报警信息移到下面 -->
                    <div class="panel-card alert-info-card">
                        <h3 class="panel-title">🚨 最新报警信息</h3>
                        <div class="latest-info-list" id="latest-info">
                            <div class="loading">正在加载最新信息...</div>
                        </div>
                    </div>
                </section>
            </div>


        </main>


    </div>

    <!-- 异常信息弹窗 -->
    <div id="alarm-modal" class="modal" onclick="if(event.target === this) closeAlarmModal()">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">2024年1月15日 - 异常报告</h3>
                <span class="close" id="close-modal" onclick="closeAlarmModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="alarm-summary">
                    <div class="summary-card">
                        <h4>📊 异常统计</h4>
                        <div class="alarm-stats">
                            <div class="stat-item-modal">
                                <span class="label">总报警数</span>
                                <span class="value" id="total-alarms">-</span>
                            </div>
                            <div class="stat-item-modal">
                                <span class="label">涉及设备</span>
                                <span class="value" id="affected-devices">-</span>
                            </div>
                            <div class="stat-item-modal">
                                <span class="label">持续时间</span>
                                <span class="value" id="total-duration">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alarm-chart-section">
                    <h4>📈 报警趋势图</h4>
                    <div class="chart-container-modal">
                        <canvas id="alarmTrendChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <div class="alarm-details">
                    <h4>🔍 详细报警信息</h4>
                    <div class="alarm-list" id="alarm-list">
                        <div class="loading">正在加载报警详情...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 设备状态列表模态窗口 -->
    <div id="deviceStatusModal" class="modal">
        <div class="modal-content device-status-modal-content">
            <div class="modal-header">
                <h3 id="statusModalTitle">设备列表</h3>
                <span class="close" id="statusModalClose">&times;</span>
            </div>
            <div class="modal-body">
                <div class="device-status-table-container">
                    <table class="device-status-table">
                        <thead>
                            <tr>
                                <th>设备名称</th>
                                <th>加工产品及工序</th>
                                <th>今日产出</th>
                                <th>当前OEE</th>
                                <th>设备OEE</th>
                                <th>开机率</th>
                                <th>设备状态</th>
                                <th id="alarmInfoColumn" style="display: none;">报警信息</th>
                            </tr>
                        </thead>
                        <tbody id="deviceStatusTableBody">
                            <!-- 设备数据将在这里动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js?v=20250723"></script>
    
    <!-- 强制修复四个状态卡片布局 -->
    <script>
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 强制设置stats-cards为flex布局
            const statsCards = document.querySelector('.stats-cards');
            if (statsCards) {
                // 直接设置内联样式，优先级最高
                statsCards.style.cssText = `
                    display: flex !important;
                    justify-content: space-between !important;
                    align-items: stretch !important;
                    gap: 10px !important;
                    width: 100% !important;
                    flex-wrap: nowrap !important;
                `;
            }
            
            // 设置每个stat-card的样式
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.style.cssText += `
                    flex: 1 1 0 !important;
                    min-width: 0 !important;
                    max-width: calc(25% - 8px) !important;
                    width: auto !important;
                    overflow: visible !important;
                    box-sizing: border-box !important;
                    border-radius: 15px !important;
                `;
            });
        });
    </script>
</body>
</html>